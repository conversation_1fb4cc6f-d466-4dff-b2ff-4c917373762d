import React from "react";
import { ScrollView } from "react-native";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Card } from "@/components/ui/card";
import { Badge, BadgeText } from "@/components/ui/badge";

const historyData = [
  {
    id: 1,
    type: "purchase",
    description: "Coffee Purchase",
    points: 25,
    date: "2 hours ago",
    location: "Downtown Store",
  },
  {
    id: 2,
    type: "bonus",
    description: "Welcome Bonus",
    points: 100,
    date: "1 day ago",
    location: "App Registration",
  },
  {
    id: 3,
    type: "purchase",
    description: "Lunch Combo",
    points: 45,
    date: "2 days ago",
    location: "Mall Location",
  },
  {
    id: 4,
    type: "redeem",
    description: "Free Coffee Redeemed",
    points: -500,
    date: "3 days ago",
    location: "Downtown Store",
  },
  {
    id: 5,
    type: "purchase",
    description: "Breakfast Sandwich",
    points: 15,
    date: "4 days ago",
    location: "Airport Location",
  },
];

export default function History() {
  const getPointsColor = (points: number) => {
    return points > 0 ? "text-success-600" : "text-error-600";
  };

  const getPointsPrefix = (points: number) => {
    return points > 0 ? "+" : "";
  };

  const getBadgeVariant = (type: string) => {
    switch (type) {
      case "purchase":
        return "solid";
      case "bonus":
        return "solid";
      case "redeem":
        return "outline";
      default:
        return "solid";
    }
  };

  const getBadgeText = (type: string) => {
    switch (type) {
      case "purchase":
        return "Purchase";
      case "bonus":
        return "Bonus";
      case "redeem":
        return "Redeemed";
      default:
        return "Activity";
    }
  };

  return (
    <ScrollView className="flex-1 bg-background-0">
      <Box className="px-6 py-8">
        <VStack space="xl">
          <Heading size="2xl" className="text-typography-900">
            Points History
          </Heading>

          <VStack space="md">
            {historyData.map((item) => (
              <Card key={item.id} className="p-4">
                <VStack space="sm">
                  <HStack className="justify-between items-start">
                    <VStack className="flex-1">
                      <HStack className="items-center" space="sm">
                        <Text
                          size="md"
                          className="text-typography-700 font-medium"
                        >
                          {item.description}
                        </Text>
                        <Badge variant={getBadgeVariant(item.type)}>
                          <BadgeText>{getBadgeText(item.type)}</BadgeText>
                        </Badge>
                      </HStack>
                      <Text size="sm" className="text-typography-500">
                        {item.location}
                      </Text>
                    </VStack>
                    <VStack className="items-end">
                      <Text
                        size="lg"
                        className={`font-bold ${getPointsColor(item.points)}`}
                      >
                        {getPointsPrefix(item.points)}
                        {Math.abs(item.points)}
                      </Text>
                      <Text size="xs" className="text-typography-400">
                        {item.date}
                      </Text>
                    </VStack>
                  </HStack>
                </VStack>
              </Card>
            ))}
          </VStack>

          {/* Summary Card */}
          <Card className="p-6 bg-primary-50">
            <VStack space="md">
              <Heading size="md" className="text-primary-900">
                This Month&apos;s Summary
              </Heading>
              <HStack className="justify-between">
                <VStack>
                  <Text size="sm" className="text-primary-700">
                    Points Earned
                  </Text>
                  <Text size="xl" className="text-primary-900 font-bold">
                    +185
                  </Text>
                </VStack>
                <VStack>
                  <Text size="sm" className="text-primary-700">
                    Points Redeemed
                  </Text>
                  <Text size="xl" className="text-primary-900 font-bold">
                    -500
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </Card>
        </VStack>
      </Box>
    </ScrollView>
  );
}

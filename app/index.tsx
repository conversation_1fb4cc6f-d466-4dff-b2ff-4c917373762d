import React, { useEffect } from "react";
import { router } from "expo-router";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { useAuth } from "@/contexts/AuthContext";
import Gradient from "@/assets/Icons/Gradient";

export default function SplashScreen() {
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && user) {
      // User is authenticated, redirect to main app
      router.replace("/(tabs)");
    }
  }, [user, loading]);

  // Show loading spinner while checking auth state
  if (loading) {
    return (
      <Box className="flex-1 bg-primary-600 justify-center items-center">
        <Box className="absolute h-[500px] w-[500px] lg:w-[700px] lg:h-[700px]">
          <Gradient />
        </Box>

        <VStack space="xl" className="items-center z-10">
          <VStack space="md" className="items-center">
            <Heading size="3xl" className="text-white font-bold">
              Indie Points
            </Heading>
            <Text size="lg" className="text-white/80 text-center">
              Earn rewards with every purchase
            </Text>
          </VStack>

          <Spinner size="large" color="white" />
        </VStack>
      </Box>
    );
  }

  // Show splash screen with auth buttons when not authenticated
  return (
    <Box className="flex-1 bg-primary-600 justify-center items-center px-6">
      <Box className="absolute h-[500px] w-[500px] lg:w-[700px] lg:h-[700px]">
        <Gradient />
      </Box>

      <VStack space="2xl" className="items-center z-10 w-full max-w-md">
        <VStack space="xl" className="items-center">
          <VStack space="md" className="items-center">
            <Heading size="3xl" className="text-white font-bold text-center">
              Indie Points
            </Heading>
            <Text size="lg" className="text-white/80 text-center">
              Earn rewards with every purchase
            </Text>
          </VStack>
        </VStack>

        <VStack space="md" className="w-full">
          <Button
            size="lg"
            className="w-full bg-white"
            onPress={() => router.push("/(auth)/sign-in")}
          >
            <ButtonText className="text-primary-600 font-semibold">
              Sign In
            </ButtonText>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="w-full border-white"
            onPress={() => router.push("/(auth)/sign-up")}
          >
            <ButtonText className="text-white font-semibold">
              Sign Up
            </ButtonText>
          </Button>
        </VStack>
      </VStack>
    </Box>
  );
}

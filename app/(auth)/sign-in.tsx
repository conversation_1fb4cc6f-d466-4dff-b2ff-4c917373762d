import React, { useEffect } from "react";
import { Link, router } from "expo-router";
import { Box } from "@/components/ui/box";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText, ButtonSpinner } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from "@/components/ui/form-control";
import { useAuthForm } from "@/hooks/useAuthForm";
import { useAuth } from "@/contexts/AuthContext";

export default function SignIn() {
  const { user } = useAuth();
  const { formState, errors, loading, updateField, handleSubmit } =
    useAuthForm("signin");

  useEffect(() => {
    if (user) {
      router.replace("/(tabs)");
    }
  }, [user]);

  const onSubmit = async () => {
    await handleSubmit();
    if (!errors.general && user) {
      router.replace("/(tabs)");
    }
  };

  return (
    <Box className="flex-1 bg-background-0 justify-center px-6">
      <VStack space="xl" className="w-full max-w-md mx-auto">
        <VStack space="md" className="items-center">
          <Heading size="2xl" className="text-typography-900">
            Welcome Back
          </Heading>
          <Text size="md" className="text-typography-500 text-center">
            Sign in to your account to continue
          </Text>
        </VStack>

        <VStack space="lg">
          <FormControl isInvalid={!!errors.email}>
            <FormControlLabel>
              <FormControlLabelText>Email</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type="text"
                placeholder="Enter your email"
                value={formState.email}
                onChangeText={(text) => updateField("email", text)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.email}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          <FormControl isInvalid={!!errors.password}>
            <FormControlLabel>
              <FormControlLabelText>Password</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                type="password"
                placeholder="Enter your password"
                value={formState.password}
                onChangeText={(text) => updateField("password", text)}
                secureTextEntry
                autoComplete="current-password"
              />
            </Input>
            <FormControlError>
              <FormControlErrorText>{errors.password}</FormControlErrorText>
            </FormControlError>
          </FormControl>

          {errors.general && (
            <Text size="sm" className="text-error-500 text-center">
              {errors.general}
            </Text>
          )}

          <Button onPress={onSubmit} isDisabled={loading} className="w-full">
            {loading && <ButtonSpinner />}
            <ButtonText>{loading ? "Signing In..." : "Sign In"}</ButtonText>
          </Button>

          <HStack className="justify-center">
            <Link href="/(auth)/forgot-password" asChild>
              <Text size="sm" className="text-primary-600">
                Forgot Password?
              </Text>
            </Link>
          </HStack>
        </VStack>

        <HStack className="justify-center items-center" space="xs">
          <Text size="sm" className="text-typography-500">
            Don&apos;t have an account?
          </Text>
          <Link href="/(auth)/sign-up" asChild>
            <Text size="sm" className="text-primary-600 font-medium">
              Sign Up
            </Text>
          </Link>
        </HStack>
      </VStack>
    </Box>
  );
}

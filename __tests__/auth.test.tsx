import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(() => Promise.resolve({ data: { session: null } })),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      })),
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      resetPasswordForEmail: jest.fn(),
      updateUser: jest.fn(),
    }
  }
}));

// Test component that uses the auth context
function TestComponent() {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <div testID="loading">Loading...</div>;
  }
  
  return (
    <div testID="auth-state">
      {user ? `Logged in as ${user.email}` : 'Not logged in'}
    </div>
  );
}

describe('AuthContext', () => {
  it('provides authentication context', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Initially should show loading
    expect(screen.getByTestId('loading')).toBeTruthy();
  });
  
  it('handles unauthenticated state', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Wait for loading to complete and check unauthenticated state
    await screen.findByTestId('auth-state');
    expect(screen.getByText('Not logged in')).toBeTruthy();
  });
});

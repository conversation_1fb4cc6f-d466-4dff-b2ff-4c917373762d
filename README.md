# Indie Points Customer App 🌟

A React Native customer loyalty app built with Expo, Gluestack UI, and Supabase authentication.

## Features

- **Authentication**: Complete auth flow with sign up, sign in, forgot password, and password reset
- **Main App Pages**: Home, Points, History, and Settings with beautiful UI
- **Splash Screen**: Smart routing based on authentication state
- **Form Validation**: Reusable form components with validation
- **Modern UI**: Built with Gluestack UI components and Tailwind CSS

## Tech Stack

- **Framework**: Expo (React Native)
- **UI Library**: Gluestack UI
- **Styling**: Tailwind CSS with NativeWind
- **Authentication**: Supabase Auth
- **Navigation**: Expo Router
- **Language**: TypeScript

## Setup Instructions

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Set up Supabase**

   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Create a `.env` file in the root directory:

   ```bash
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Start the development server**
   ```bash
   npm run start
   ```

## App Structure

```
app/
├── (auth)/                 # Authentication pages
│   ├── sign-in.tsx        # Sign in page
│   ├── sign-up.tsx        # Sign up page
│   ├── forgot-password.tsx # Forgot password page
│   └── reset-password.tsx  # Reset password page
├── (tabs)/                 # Main app tabs
│   ├── index.tsx          # Home page
│   ├── points.tsx         # Points page
│   ├── history.tsx        # History page
│   └── settings.tsx       # Settings page
├── index.tsx              # Splash screen with auth routing
└── _layout.tsx            # Root layout with providers

contexts/
└── AuthContext.tsx        # Authentication context and hooks

components/
├── forms/
│   └── FormField.tsx      # Reusable form field component
└── ui/                    # Gluestack UI components

lib/
└── supabase.ts           # Supabase client configuration
```

## Authentication Flow

1. **Splash Screen**: Checks authentication state and routes accordingly
2. **Sign Up**: Create new account with email/password
3. **Sign In**: Login with existing credentials
4. **Forgot Password**: Request password reset email
5. **Reset Password**: Set new password from reset link
6. **Protected Routes**: Automatic redirection based on auth state

## Development

- Run on iOS: `npm run ios`
- Run on Android: `npm run android`
- Run on Web: `npm run web`
- Run tests: `npm test`
- Lint code: `npm run lint`
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.
- [Nativewind](https://www.nativewind.dev/): Nativewind is a utility-first library for building native apps with Tailwind CSS.
- [Gluestack](https://gluestack.io/): Gluestack is a component library for building native apps with Tailwind CSS.

## Join the community

Join our community of developers creating universal apps.

- [gluestack-ui on GitHub](https://github.com/gluestack/gluestack-ui): View our open source ui library and contribute.
- [gluestack community](https://discord.com/channels/1050761204852858900/*********4168484914): Chat with gluestack users and ask questions.
